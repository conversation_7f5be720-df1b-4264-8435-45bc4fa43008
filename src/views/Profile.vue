<template>
  <div class="profile-container">
    <!-- 账号信息区域 -->
    <div class="account-section">
      <div class="account-info">
        <div class="avatar-container">
          <img 
            :src="userInfo.avatar" 
            :alt="userInfo.name"
            class="user-avatar"
          />
          <div class="online-status"></div>
        </div>
        <div class="user-details">
          <h3 class="user-name">{{ userInfo.name }}</h3>
          <p class="user-group">{{ userInfo.group }}</p>
          <p class="user-role">{{ userInfo.role }}</p>
        </div>
        <div class="account-actions">
          <van-icon name="setting-o" color="#7ed321" size="24" />
        </div>
      </div>
    </div>

    <!-- Tab选项区域 -->
    <div class="tabs-section">
      <van-tabs 
        v-model:active="activeTab" 
        class="custom-tabs"
        color="#7ed321"
        title-active-color="#7ed321"
        title-inactive-color="#666666"
      >
        <van-tab title="Order History" name="orders">
          <!-- 订单记录内容 -->
          <div class="orders-content">
            <div class="orders-header">
              <h4 class="orders-title">Recent Orders</h4>
              <div class="header-actions">
                <button
                  v-if="hasActiveFilters"
                  class="reset-filters-btn"
                  @click="resetFilters"
                >
                  Reset
                </button>
                <div class="filter-btn" @click="openFilterPopup">
                  <van-icon name="filter-o" color="#7ed321" size="16" />
                  <span>Filter</span>
                  <div v-if="hasActiveFilters" class="filter-badge"></div>
                </div>
              </div>
            </div>
            
            <!-- 订单列表 -->
            <div class="orders-list">
              <div
                v-for="order in ordersList"
                :key="order.id"
                class="order-item"
                @click="handleOrderClick(order)"
              >
                <div class="order-header">
                  <div class="order-info">
                    <span class="order-number">#{{ order.orderNumber }}</span>
                    <span class="order-date">{{ order.date }}</span>
                  </div>
                  <div class="order-status" :class="order.status.toLowerCase()">
                    {{ order.status }}
                  </div>
                </div>
                
                <div class="order-content">
                  <div class="order-items">
                    <div 
                      v-for="item in order.items.slice(0, 2)" 
                      :key="item.id"
                      class="order-product"
                    >
                      <img :src="item.image" :alt="item.name" class="product-thumb" />
                      <div class="product-info">
                        <span class="product-name">{{ item.name }}</span>
                        <span class="product-quantity">x{{ item.quantity }}</span>
                      </div>
                    </div>
                    <div v-if="order.items.length > 2" class="more-items">
                      +{{ order.items.length - 2 }} more items
                    </div>
                  </div>
                  
                  <div class="order-footer">
                    <div class="order-total">
                      <span class="total-amount">${{ order.total.toFixed(2) }}</span>
                    </div>
                    <div class="order-actions">
                      <button class="action-btn view-btn">View</button>
                      <button 
                        v-if="order.status === 'Delivered'"
                        class="action-btn reorder-btn"
                      >
                        Reorder
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
        
        <van-tab title="Statistics" name="stats">
          <div class="stats-content">
            <div class="coming-soon">
              <van-icon name="chart-trending-o" color="#7ed321" size="64" />
              <h3>Statistics Coming Soon</h3>
              <p>Sales reports and analytics will be available here</p>
            </div>
          </div>
        </van-tab>
        
        <van-tab title="Security" name="security">
          <div class="security-content">
            <div class="coming-soon">
              <van-icon name="shield-o" color="#7ed321" size="64" />
              <h3>Security Settings</h3>
              <p>Password and security options will be available here</p>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- Filter弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      position="bottom"
      :style="{ height: '70vh' }"
      round
      closeable
      close-icon="cross"
      @close="closeFilterPopup"
    >
      <div class="filter-popup">
        <div class="filter-header">
          <h3 class="filter-title">Filter Orders</h3>
        </div>

        <div class="filter-content">
          <!-- 时间范围筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Date Range</h4>
            <div class="date-range-options">
              <div
                v-for="range in dateRangeOptions"
                :key="range.value"
                class="date-option"
                :class="{ active: tempFilters.dateRange === range.value }"
                @click="selectDateRange(range.value)"
              >
                <span>{{ range.label }}</span>
                <van-icon
                  v-if="tempFilters.dateRange === range.value"
                  name="success"
                  color="#7ed321"
                  size="16"
                />
              </div>
            </div>
          </div>

          <!-- 订单状态筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Order Status</h4>
            <div class="status-options">
              <div
                v-for="status in statusOptions"
                :key="status.value"
                class="status-option"
                :class="{ active: tempFilters.status.includes(status.value) }"
                @click="toggleStatus(status.value)"
              >
                <div class="status-content">
                  <div class="status-indicator" :class="status.value.toLowerCase()"></div>
                  <span class="status-label">{{ status.label }}</span>
                </div>
                <div
                  v-if="tempFilters.status.includes(status.value)"
                  class="status-badge"
                >
                  <van-icon name="success" color="white" size="10" />
                </div>
              </div>
            </div>
          </div>

          <!-- 业务员筛选 -->
          <div class="filter-section">
            <h4 class="filter-section-title">Salesman</h4>
            <div class="salesman-options">
              <div
                v-for="salesman in salesmanOptions"
                :key="salesman.value"
                class="salesman-option"
                :class="{ active: tempFilters.salesman.includes(salesman.value) }"
                @click="toggleSalesman(salesman.value)"
              >
                <div class="salesman-left">
                  <van-icon name="contact" color="#7ed321" size="16" />
                  <span>{{ salesman.label }}</span>
                </div>
                <van-icon
                  v-if="tempFilters.salesman.includes(salesman.value)"
                  name="success"
                  color="#7ed321"
                  size="16"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <button class="apply-btn" @click="applyFilters">
            Apply Filters ({{ filteredOrdersCount }})
          </button>
        </div>
      </div>
    </van-popup>

    <!-- 底部TabBar -->
    <van-tabbar v-model="activeMainTab" class="custom-tabbar" @change="handleTabChange">
      <van-tabbar-item icon="home-o" name="home">
        Home
      </van-tabbar-item>
      <van-tabbar-item icon="user-o" name="profile">
        Profile
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const activeTab = ref('orders')
const activeMainTab = ref('profile')
const showFilterPopup = ref(false) // Filter弹窗显示状态

// Filter相关数据
const filters = ref({
  dateRange: 'all',
  status: [],
  salesman: []
})

// 临时筛选条件（在弹窗中修改，点击Apply后应用到filters）
const tempFilters = ref({
  dateRange: 'all',
  status: [],
  salesman: []
})

// Filter选项数据
const dateRangeOptions = ref([
  { label: 'All Time', value: 'all' },
  { label: 'Last 7 Days', value: '7days' },
  { label: 'Last 30 Days', value: '30days' },
  { label: 'Last 3 Months', value: '3months' }
])

const statusOptions = ref([
  { label: 'Delivered', value: 'Delivered' },
  { label: 'Processing', value: 'Processing' },
  { label: 'Cancelled', value: 'Cancelled' }
])

const salesmanOptions = ref([
  { label: 'John Smith', value: 'john' },
  { label: 'Alice Johnson', value: 'alice' },
  { label: 'Bob Wilson', value: 'bob' },
  { label: 'Carol Brown', value: 'carol' }
])

// 用户信息
const userInfo = ref({
  name: 'John Smith',
  group: 'Sales Team A',
  role: 'Senior Sales Representative',
  avatar: 'https://api.dicebear.com/7.x/adventurer/svg?seed=John&backgroundColor=7ed321'
})

// 模拟订单数据
const allOrdersList = ref([
  {
    id: 1,
    orderNumber: 'ORD-2024-001',
    date: '2025-06-24', // 昨天
    dateObj: new Date('2025-06-24'),
    status: 'Delivered',
    salesman: 'john',
    total: 156.99,
    items: [
      {
        id: 1,
        name: 'Fresh Organic Apples',
        quantity: 3,
        image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=60&h=60&fit=crop'
      },
      {
        id: 2,
        name: 'Premium Avocados',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=60&h=60&fit=crop'
      },
      {
        id: 3,
        name: 'Organic Bananas',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 2,
    orderNumber: 'ORD-2024-002',
    date: '2025-06-22', // 3天前
    dateObj: new Date('2025-06-22'),
    status: 'Processing',
    salesman: 'alice',
    total: 89.50,
    items: [
      {
        id: 4,
        name: 'Fresh Strawberries',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=60&h=60&fit=crop'
      },
      {
        id: 5,
        name: 'Organic Bananas',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 3,
    orderNumber: 'ORD-2024-003',
    date: '2025-06-15', // 10天前
    dateObj: new Date('2025-06-15'),
    status: 'Cancelled',
    salesman: 'bob',
    total: 234.75,
    items: [
      {
        id: 6,
        name: 'Premium Avocados',
        quantity: 5,
        image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 4,
    orderNumber: 'ORD-2024-004',
    date: '2025-05-28', // 27天前
    dateObj: new Date('2025-05-28'),
    status: 'Delivered',
    salesman: 'carol',
    total: 78.25,
    items: [
      {
        id: 7,
        name: 'Fresh Strawberries',
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=60&h=60&fit=crop'
      }
    ]
  },
  {
    id: 5,
    orderNumber: 'ORD-2024-005',
    date: '2025-03-20', // 3个月前
    dateObj: new Date('2025-03-20'),
    status: 'Processing',
    salesman: 'john',
    total: 145.80,
    items: [
      {
        id: 8,
        name: 'Fresh Organic Apples',
        quantity: 2,
        image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=60&h=60&fit=crop'
      }
    ]
  }
])

// 计算属性
const ordersList = computed(() => {
  let filtered = allOrdersList.value

  // 按日期范围筛选
  if (filters.value.dateRange !== 'all') {
    const now = new Date()
    let cutoffDate

    switch (filters.value.dateRange) {
      case '7days':
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30days':
        cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '3months':
        cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
    }

    if (cutoffDate) {
      filtered = filtered.filter(order => order.dateObj >= cutoffDate)
    }
  }

  // 按状态筛选
  if (filters.value.status.length > 0) {
    filtered = filtered.filter(order => filters.value.status.includes(order.status))
  }

  // 按业务员筛选
  if (filters.value.salesman.length > 0) {
    filtered = filtered.filter(order => filters.value.salesman.includes(order.salesman))
  }

  return filtered
})

const hasActiveFilters = computed(() => {
  return filters.value.dateRange !== 'all' ||
         filters.value.status.length > 0 ||
         filters.value.salesman.length > 0
})

const filteredOrdersCount = computed(() => {
  // 基于tempFilters计算预览数量
  let filtered = allOrdersList.value

  // 按日期范围筛选
  if (tempFilters.value.dateRange !== 'all') {
    const now = new Date()
    let cutoffDate

    switch (tempFilters.value.dateRange) {
      case '7days':
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30days':
        cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '3months':
        cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
    }

    if (cutoffDate) {
      filtered = filtered.filter(order => order.dateObj >= cutoffDate)
    }
  }

  // 按状态筛选
  if (tempFilters.value.status.length > 0) {
    filtered = filtered.filter(order => tempFilters.value.status.includes(order.status))
  }

  // 按业务员筛选
  if (tempFilters.value.salesman.length > 0) {
    filtered = filtered.filter(order => tempFilters.value.salesman.includes(order.salesman))
  }

  return filtered.length
})

// 处理订单点击
const handleOrderClick = (order) => {
  console.log('查看订单详情:', order.orderNumber)
  // 这里可以添加跳转到订单详情页面的逻辑
}

// Filter相关方法
const selectDateRange = (range) => {
  tempFilters.value.dateRange = range
}

const toggleStatus = (status) => {
  const index = tempFilters.value.status.indexOf(status)
  if (index > -1) {
    tempFilters.value.status.splice(index, 1)
  } else {
    tempFilters.value.status.push(status)
  }
}

const toggleSalesman = (salesman) => {
  const index = tempFilters.value.salesman.indexOf(salesman)
  if (index > -1) {
    tempFilters.value.salesman.splice(index, 1)
  } else {
    tempFilters.value.salesman.push(salesman)
  }
}

const resetFilters = () => {
  filters.value = {
    dateRange: 'all',
    status: [],
    salesman: []
  }
  tempFilters.value = {
    dateRange: 'all',
    status: [],
    salesman: []
  }
}

const applyFilters = () => {
  // 将临时筛选条件应用到实际筛选条件
  filters.value = {
    dateRange: tempFilters.value.dateRange,
    status: [...tempFilters.value.status],
    salesman: [...tempFilters.value.salesman]
  }
  showFilterPopup.value = false
  console.log('应用筛选条件:', filters.value)
}

const openFilterPopup = () => {
  // 打开弹窗时初始化临时筛选条件为当前实际筛选条件
  tempFilters.value = {
    dateRange: filters.value.dateRange,
    status: [...filters.value.status],
    salesman: [...filters.value.salesman]
  }
  showFilterPopup.value = true
}

const closeFilterPopup = () => {
  // 关闭弹窗时恢复临时筛选条件为当前实际筛选条件
  tempFilters.value = {
    dateRange: filters.value.dateRange,
    status: [...filters.value.status],
    salesman: [...filters.value.salesman]
  }
  showFilterPopup.value = false
}

// 处理底部Tab切换
const handleTabChange = (name) => {
  if (name === 'home') {
    router.push('/home')
  }
}
</script>

<style scoped>
/* Profile页面容器 */
.profile-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 60px; /* 为底部TabBar留出空间 */
}

/* 账号信息区域 */
.account-section {
  background: linear-gradient(135deg, #7ed321 0%, #6bc91a 100%);
  padding: 40px 20px 30px;
  color: white;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-container {
  position: relative;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #2ecc71;
  border: 3px solid white;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: white;
}

.user-group {
  font-size: 14px;
  margin: 0 0 2px 0;
  color: rgba(255, 255, 255, 0.9);
}

.user-role {
  font-size: 12px;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
}

.account-actions {
  padding: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.account-actions:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Tab区域 */
.tabs-section {
  background: white;
  margin-top: -10px;
  border-radius: 16px 16px 0 0;
  min-height: calc(100vh - 200px);
}

.custom-tabs {
  --van-tabs-line-height: 44px;
  --van-tabs-card-height: 44px;
}

:deep(.van-tabs__wrap) {
  padding: 0 20px;
  background: white;
  border-radius: 16px 16px 0 0;
}

:deep(.van-tabs__nav) {
  background: white;
}

:deep(.van-tab) {
  font-weight: 500;
}

/* 订单内容 */
.orders-content {
  padding: 20px;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.orders-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reset-filters-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #e74c3c 100%);
  border: none;
  color: white;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  position: relative;
  overflow: hidden;
}

.reset-filters-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
}

.reset-filters-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
}

.filter-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #7ed321;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  height: 32px;
}

.filter-btn:hover {
  background-color: rgba(126, 211, 33, 0.1);
}

.filter-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid white;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #7ed321;
}

.order-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #6bc91a;
}



.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.order-number {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.order-date {
  font-size: 12px;
  color: #666666;
}

.order-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.delivered {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.order-status.processing {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.order-status.cancelled {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* 订单商品 */
.order-items {
  margin-bottom: 12px;
}

.order-product {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.product-thumb {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-name {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.product-quantity {
  font-size: 12px;
  color: #666666;
}

.more-items {
  font-size: 12px;
  color: #7ed321;
  font-weight: 500;
  margin-left: 52px;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.order-total {
  display: flex;
  align-items: center;
}

.total-amount {
  font-size: 16px;
  font-weight: 700;
  color: #7ed321;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.view-btn {
  background: rgba(126, 211, 33, 0.1);
  color: #7ed321;
}

.view-btn:hover {
  background: rgba(126, 211, 33, 0.2);
}

.reorder-btn {
  background: #7ed321;
  color: white;
}

.reorder-btn:hover {
  background: #6bc91a;
}

/* 即将推出内容 */
.stats-content,
.security-content {
  padding: 40px 20px;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
}

.coming-soon h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 16px 0 8px 0;
}

.coming-soon p {
  font-size: 14px;
  color: #666666;
  margin: 0;
}

/* 底部TabBar */
.custom-tabbar {
  --van-tabbar-background: #ffffff;
  --van-tabbar-item-text-color: #999999;
  --van-tabbar-item-active-color: #7ed321;
  --van-tabbar-item-active-background: transparent;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.van-tabbar-item__icon) {
  font-size: 20px;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  font-weight: 500;
}

/* Filter弹窗样式 */
.filter-popup {
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 16px 60px 16px 20px; /* 右侧留出关闭按钮空间 */
  border-bottom: 1px solid #e5e5e5;
  height: 52px; /* 固定高度 */
  flex-shrink: 0;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 16px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

/* 日期范围选项 */
.date-range-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.date-option {
  padding: 12px 16px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 44px;
}

.date-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.date-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
  color: #7ed321;
}

/* 状态选项 */
.status-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.status-option {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 12px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: #666666;
}

.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: #7ed321;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.status-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.delivered {
  background: #2ecc71;
}

.status-indicator.processing {
  background: #3498db;
}

.status-indicator.cancelled {
  background: #e74c3c;
}

/* 业务员选项 */
.salesman-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.salesman-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
}

.salesman-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.salesman-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.salesman-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
}

/* Filter底部 */
.filter-footer {
  padding: 16px;
  border-top: 1px solid #e5e5e5;
  background: white;
}

.apply-btn {
  width: 100%;
  height: 48px;
  background: #7ed321;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  background: #6bc91a;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .account-section {
    padding: 30px 16px 20px;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
  }

  .user-name {
    font-size: 18px;
  }

  .orders-content {
    padding: 16px;
  }

  .order-item {
    padding: 12px;
  }

  .date-range-options {
    grid-template-columns: 1fr;
  }
}
</style>

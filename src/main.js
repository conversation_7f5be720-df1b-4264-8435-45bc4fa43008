import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { Button, Field, Form, Icon, Search, Tabbar, TabbarItem, Popup, Dialog, Picker } from 'vant'
import 'vant/lib/index.css'
import './style.css'
import App from './App.vue'
import Login from './views/Login.vue'
import Home from './views/Home.vue'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)

// 注册Vant组件
app.use(Button)
app.use(Field)
app.use(Form)
app.use(Icon)
app.use(Search)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Popup)
app.use(Dialog)
app.use(Picker)
app.use(router)

app.mount('#app')
